# Copyright (c) Meta Platforms, Inc. and affiliates
from typing import Dict, List, Optional
from ..models.product import Item


class Store:

    def __init__(self, cap: int=20):
        self.cap = cap
        self._data: Dict[str, Item] = {}
        self._map: Dict[int, str] = {}

    def put(self, obj: Item, pos: Optional[int]=None) ->bool:
        if obj.code in self._data:
            curr = self._data[obj.code]
            curr.count += obj.count
            return True
        if pos is not None:
            if pos < 0 or pos >= self.cap:
                return False
            if pos in self._map:
                return False
            self._map[pos] = obj.code
        else:
            for i in range(self.cap):
                if i not in self._map:
                    self._map[i] = obj.code
                    break
            else:
                return False
        self._data[obj.code] = obj
        return True

    def rm(self, code: str) ->bool:
        if code not in self._data:
            return False
        for k, v in list(self._map.items()):
            if v == code:
                del self._map[k]
        del self._data[code]
        return True

    def get(self, code: str) ->Optional[Item]:
        return self._data.get(code)

    def get_at(self, pos: int) ->Optional[Item]:
        if pos not in self._map:
            return None
        code = self._map[pos]
        return self._data.get(code)

    def ls(self) ->List[Item]:
        return [obj for obj in self._data.values() if obj.check()]

    def find(self, code: str) ->Optional[int]:
        for k, v in self._map.items():
            if v == code:
                return k
        return None
