/* Copyright (c) Meta Platforms, Inc. and affiliates */
/* DocAgent - Docstring Evaluation System Styles */

/* General Styles */
body {
    background-color: #f8f9fa;
}

.card {
    border-radius: 0.5rem;
    overflow: hidden;
}

.card-header {
    border-bottom: none;
}

/* Table Styles */
.table {
    font-size: 0.9rem;
}

.table th {
    font-weight: 600;
}

.table-responsive {
    max-height: 70vh;
    overflow-y: auto;
}

/* Button Styles */
.evaluate-btn {
    font-size: 0.75rem;
    padding: 0.2rem 0.5rem;
}

/* Modal Styles */
.modal-content {
    border-radius: 0.5rem;
    overflow: hidden;
}

.modal-header {
    border-bottom: none;
}

.modal-footer {
    border-top: none;
}

/* Docstring content display */
pre#docstringContent {
    max-height: 300px;
    overflow-y: auto;
    font-size: 0.9rem;
    white-space: pre-wrap;
}

/* Badges */
.badge {
    font-weight: 500;
    padding: 0.35rem 0.65rem;
}

/* Alert <PERSON> */
.alert {
    border-radius: 0.5rem;
}

/* Responsive Adjustments */
@media (max-width: 992px) {
    .table {
        font-size: 0.8rem;
    }
    
    .evaluate-btn {
        font-size: 0.7rem;
        padding: 0.15rem 0.4rem;
    }
    
    .badge {
        font-size: 0.7rem;
        padding: 0.25rem 0.5rem;
    }
}

/* Custom scrollbar */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: #888;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #555;
} 