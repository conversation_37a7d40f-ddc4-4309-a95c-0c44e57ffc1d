# Copyright (c) Meta Platforms, Inc. and affiliates
import json
import random
import os
import sys
from pathlib import Path
from typing import Dict, Any, List, Optional, Tuple
from dataclasses import dataclass

# Add the project root to path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from src.evaluator.helpfulness_summary import DocstringSummaryEvaluator
from src.evaluator.helpfulness_description import DocstringDescriptionEvaluator  
from src.evaluator.helpfulness_parameters import DocstringParametersEvaluator
from src.agent.llm.openai_llm import OpenAILLM

@dataclass
class EvaluationResult:
    """Store the results of a single evaluation."""
    system: str
    component_id: str
    aspect: str
    score: int
    suggestion: str

class DocstringHelpfulnessEvaluatorAblation:
    """Evaluates the helpfulness of docstrings generated by different systems."""
    
    SYSTEMS = [
        "docassist-codellama34b-random-file",
        "docassist-codellama34b-random-node",
        "docassist-gpt4o_mini-random-file",
        "docassist-gpt4o_mini-random-node",
        "docassist-codellama34b",
        "docassist-gpt4o_mini",
    ]
    
    ASPECTS = ["summary", "description", "parameters"]
    
    def __init__(self, data_path: str, output_dir: str, api_key: str, model: str = "gpt-4o"):
        """Initialize the evaluator.
        
        Args:
            data_path: Path to the completeness evaluation data
            output_dir: Directory to store evaluation results
            api_key: OpenAI API key
            model: LLM model to use for evaluation
        """
        self.data_path = data_path
        self.output_dir = output_dir
        self.llm = OpenAILLM(api_key=api_key, model=model)
        
        # Initialize evaluators for each aspect
        self.evaluators = {
            "summary": DocstringSummaryEvaluator(),
            "description": DocstringDescriptionEvaluator(),
            "parameters": DocstringParametersEvaluator()
        }
        
        # Load evaluation data
        with open(self.data_path, 'r') as f:
            self.data = json.load(f)
            
        # Create output directory if it doesn't exist
        os.makedirs(self.output_dir, exist_ok=True)
    
    def sample_components(self, n: Optional[int] = 50, seed: int = 42) -> List[str]:
        """Randomly sample code components where all systems have valid docstrings.
        
        Args:
            n: Number of components to sample. If None, return all valid components.
            seed: Random seed for reproducibility
            
        Returns:
            List of component IDs
        """
        random.seed(seed)
        
        # Filter components where all systems have valid docstrings
        valid_components = []
        for component_id, component_data in self.data.items():
            # Check if all systems have docstrings
            has_all_docstrings = True
            for system in self.SYSTEMS:
                if system not in component_data.get("docstrings", {}):
                    has_all_docstrings = False
                    break
                
                # Check if docstring is not empty
                docstring = component_data["docstrings"].get(system, {}).get("docstring", "")
                if not docstring or docstring == "example string":
                    has_all_docstrings = False
                    break
            
            if has_all_docstrings:
                valid_components.append(component_id)
        
        # If n is None, return all valid components
        if n is None:
            print(f"Using all {len(valid_components)} valid components")
            return valid_components
            
        # Sample n components
        if len(valid_components) < n:
            print(f"Warning: Only {len(valid_components)} components have valid docstrings for all systems")
            return valid_components
        
        return random.sample(valid_components, n)
    
    def evaluate_component(self, component_id: str) -> List[EvaluationResult]:
        """Evaluate docstrings from all systems for a given component.
        
        Args:
            component_id: Component ID
            
        Returns:
            List of evaluation results
        """
        component_data = self.data[component_id]
        results = []
        
        component_type = component_data.get("type", "function")
        source_code = component_data.get("source_code", "")
        
        for system in self.SYSTEMS:
            if system not in component_data.get("docstrings", {}):
                continue
                
            system_data = component_data["docstrings"][system]
            docstring = system_data.get("docstring", "")
            
            # Skip if docstring is empty or the example placeholder
            if not docstring or docstring == "example string":
                continue
            
            print(f"  Evaluating system: {system}")
            # Evaluate each aspect
            for aspect in self.ASPECTS:
                # Check if the aspect is present in the docstring
                element_scores = system_data.get("element_scores", {})
                if aspect not in element_scores or not element_scores[aspect]:
                    print(f"    Skipping aspect '{aspect}' - not present in docstring")
                    continue
                
                print(f"    Evaluating aspect: {aspect}")
                try:
                    # Get the evaluator for this aspect
                    evaluator = self.evaluators[aspect]
                    
                    # Create prompt for evaluation
                    prompt = evaluator.get_evaluation_prompt(source_code, docstring, component_type)
                    
                    # Call LLM for evaluation
                    messages = [
                        self.llm.format_message("system", "You are an expert docstring quality evaluator."),
                        self.llm.format_message("user", prompt)
                    ]
                    
                    response = self.llm.generate(messages, temperature=0.1, max_tokens=1024)
                    
                    # Parse response
                    score, suggestion = evaluator.parse_llm_response(response)
                    
                    print(f"      Score: {score}")
                    
                    # Store result
                    result = EvaluationResult(
                        system=system,
                        component_id=component_id,
                        aspect=aspect,
                        score=score,
                        suggestion=suggestion
                    )
                    
                    results.append(result)
                except Exception as e:
                    print(f"    Error evaluating {aspect}: {str(e)}")
                    # Continue with other evaluations
        
        return results
    
    def run_evaluation(self, n_samples: int = 50, seed: int = 42) -> Dict[str, Any]:
        """Run the helpfulness evaluation on sampled components.
        
        Args:
            n_samples: Number of components to sample
            seed: Random seed for reproducibility
            
        Returns:
            Evaluation results
        """
        # Sample components
        component_ids = self.sample_components(n_samples, seed)
        
        # Evaluate each component
        all_results = []
        for component_id in component_ids:
            print(f"Evaluating component: {component_id}")
            results = self.evaluate_component(component_id)
            all_results.extend(results)
        
        # Organize results
        results_dict = {
            "metadata": {
                "n_samples": len(component_ids),
                "seed": seed,
                "systems": self.SYSTEMS,
                "aspects": self.ASPECTS
            },
            "component_ids": component_ids,
            "results": [
                {
                    "system": r.system,
                    "component_id": r.component_id,
                    "aspect": r.aspect,
                    "score": r.score,
                    "suggestion": r.suggestion
                }
                for r in all_results
            ]
        }
        
        # Save results to file
        output_path = os.path.join(self.output_dir, "helpfulness_evaluation_results.json")
        with open(output_path, 'w') as f:
            json.dump(results_dict, f, indent=2)
        
        # Generate statistics
        stats = self.calculate_statistics(results_dict)
        
        # Save statistics to file
        stats_path = os.path.join(self.output_dir, "helpfulness_evaluation_stats.md")
        with open(stats_path, 'w') as f:
            f.write(self.format_statistics_markdown(stats))
        
        return results_dict
    
    def calculate_statistics(self, results: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate statistics from evaluation results.
        
        Args:
            results: Evaluation results
            
        Returns:
            Statistics
        """
        stats = {
            "overall": {},
            "by_system": {},
            "by_aspect": {},
            "by_system_and_aspect": {}
        }
        
        # Calculate overall average
        scores = [r["score"] for r in results["results"]]
        stats["overall"]["average_score"] = sum(scores) / len(scores) if scores else 0
        stats["overall"]["count"] = len(scores)
        
        # Calculate average by system
        for system in self.SYSTEMS:
            system_scores = [r["score"] for r in results["results"] if r["system"] == system]
            stats["by_system"][system] = {
                "average_score": sum(system_scores) / len(system_scores) if system_scores else 0,
                "count": len(system_scores)
            }
        
        # Calculate average by aspect
        for aspect in self.ASPECTS:
            aspect_scores = [r["score"] for r in results["results"] if r["aspect"] == aspect]
            stats["by_aspect"][aspect] = {
                "average_score": sum(aspect_scores) / len(aspect_scores) if aspect_scores else 0,
                "count": len(aspect_scores)
            }
        
        # Calculate average by system and aspect
        for system in self.SYSTEMS:
            stats["by_system_and_aspect"][system] = {}
            for aspect in self.ASPECTS:
                scores = [r["score"] for r in results["results"] 
                         if r["system"] == system and r["aspect"] == aspect]
                stats["by_system_and_aspect"][system][aspect] = {
                    "average_score": sum(scores) / len(scores) if scores else 0,
                    "count": len(scores)
                }
        
        return stats
    
    def format_statistics_markdown(self, stats: Dict[str, Any]) -> str:
        """Format statistics as markdown.
        
        Args:
            stats: Statistics
            
        Returns:
            Markdown representation of statistics
        """
        md = "# Docstring Helpfulness Evaluation Results\n\n"
        
        # Overall statistics
        md += "## Overall Statistics\n\n"
        md += f"- Average Score: {stats['overall']['average_score']:.2f}\n"
        md += f"- Number of Evaluations: {stats['overall']['count']}\n\n"
        
        # By system
        md += "## Results by System\n\n"
        md += "| System | Average Score | Count |\n"
        md += "| ------ | ------------- | ----- |\n"
        for system, system_stats in stats["by_system"].items():
            md += f"| {system} | {system_stats['average_score']:.2f} | {system_stats['count']} |\n"
        md += "\n"
        
        # By aspect
        md += "## Results by Aspect\n\n"
        md += "| Aspect | Average Score | Count |\n"
        md += "| ------ | ------------- | ----- |\n"
        for aspect, aspect_stats in stats["by_aspect"].items():
            md += f"| {aspect} | {aspect_stats['average_score']:.2f} | {aspect_stats['count']} |\n"
        md += "\n"
        
        # By system and aspect
        md += "## Results by System and Aspect\n\n"
        md += "| System | Aspect | Average Score | Count |\n"
        md += "| ------ | ------ | ------------- | ----- |\n"
        for system, aspects in stats["by_system_and_aspect"].items():
            for aspect, aspect_stats in aspects.items():
                md += f"| {system} | {aspect} | {aspect_stats['average_score']:.2f} | {aspect_stats['count']} |\n"
        
        return md

def main():
    """Run the docstring helpfulness evaluation."""
    # Configuration
    data_path = "experiments/eval/results/completeness_evaluation_ablation_cleaned.json"
    output_dir = "experiments/eval/results/helpfulness_ablation"
    
    # Get API key from config
    with open("config/agent_config.yaml", 'r') as f:
        config = yaml.safe_load(f)
        api_key = config["llm"]["api_key"]
        model = config["llm"]["model"]
    
    # Run evaluation
    evaluator = DocstringHelpfulnessEvaluatorAblation(data_path, output_dir, api_key, model)
    evaluator.run_evaluation(n_samples=50, seed=42)

if __name__ == "__main__":
    import yaml
    main() 