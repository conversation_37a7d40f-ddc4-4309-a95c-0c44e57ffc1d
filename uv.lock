# This file was autogenerated by uv via the following command:
#    uv pip compile requirements.txt -o uv.lock
accelerate==1.7.0
    # via -r requirements.txt
annotated-types==0.7.0
    # via
    #   -r requirements.txt
    #   pydantic
anthropic==0.54.0
    # via
    #   -r requirements.txt
    #   langchain-anthropic
anyio==4.9.0
    # via
    #   -r requirements.txt
    #   anthropic
    #   httpx
    #   openai
appnope==0.1.4
    # via
    #   -r requirements.txt
    #   ipykernel
astor==0.8.1
    # via -r requirements.txt
asttokens==3.0.0
    # via
    #   -r requirements.txt
    #   stack-data
bidict==0.23.1
    # via
    #   -r requirements.txt
    #   python-socketio
blinker==1.9.0
    # via
    #   -r requirements.txt
    #   flask
cachetools==5.5.2
    # via
    #   -r requirements.txt
    #   google-auth
certifi==2025.6.15
    # via
    #   -r requirements.txt
    #   httpcore
    #   httpx
    #   requests
charset-normalizer==3.4.2
    # via
    #   -r requirements.txt
    #   requests
click==8.2.1
    # via
    #   -r requirements.txt
    #   flask
code2flow==2.5.1
    # via -r requirements.txt
colorama==0.4.6
    # via -r requirements.txt
comm==0.2.2
    # via
    #   -r requirements.txt
    #   ipykernel
debugpy==1.8.14
    # via
    #   -r requirements.txt
    #   ipykernel
decorator==5.2.1
    # via
    #   -r requirements.txt
    #   ipython
distro==1.9.0
    # via
    #   -r requirements.txt
    #   anthropic
    #   openai
dnspython==2.7.0
    # via
    #   -r requirements.txt
    #   eventlet
eventlet==0.40.0
    # via -r requirements.txt
executing==2.2.0
    # via
    #   -r requirements.txt
    #   stack-data
filelock==3.18.0
    # via
    #   -r requirements.txt
    #   huggingface-hub
    #   torch
    #   transformers
flask==3.1.1
    # via
    #   -r requirements.txt
    #   flask-socketio
flask-socketio==5.5.1
    # via -r requirements.txt
fsspec==2025.5.1
    # via
    #   -r requirements.txt
    #   huggingface-hub
    #   torch
google-ai-generativelanguage==0.6.15
    # via
    #   -r requirements.txt
    #   google-generativeai
google-api-core==2.25.1
    # via
    #   -r requirements.txt
    #   google-ai-generativelanguage
    #   google-api-python-client
    #   google-generativeai
google-api-python-client==2.172.0
    # via
    #   -r requirements.txt
    #   google-generativeai
google-auth==2.40.3
    # via
    #   -r requirements.txt
    #   google-ai-generativelanguage
    #   google-api-core
    #   google-api-python-client
    #   google-auth-httplib2
    #   google-generativeai
google-auth-httplib2==0.2.0
    # via
    #   -r requirements.txt
    #   google-api-python-client
google-generativeai==0.8.5
    # via -r requirements.txt
googleapis-common-protos==1.70.0
    # via
    #   -r requirements.txt
    #   google-api-core
    #   grpcio-status
greenlet==3.2.3
    # via
    #   -r requirements.txt
    #   eventlet
grpcio==1.73.0
    # via
    #   -r requirements.txt
    #   google-api-core
    #   grpcio-status
grpcio-status==1.71.0
    # via
    #   -r requirements.txt
    #   google-api-core
h11==0.16.0
    # via
    #   -r requirements.txt
    #   httpcore
    #   wsproto
hf-xet==1.1.4
    # via
    #   -r requirements.txt
    #   huggingface-hub
httpcore==1.0.9
    # via
    #   -r requirements.txt
    #   httpx
httplib2==0.22.0
    # via
    #   -r requirements.txt
    #   google-api-python-client
    #   google-auth-httplib2
httpx==0.28.1
    # via
    #   -r requirements.txt
    #   anthropic
    #   langgraph-sdk
    #   langsmith
    #   ollama
    #   openai
huggingface-hub==0.33.0
    # via
    #   -r requirements.txt
    #   accelerate
    #   tokenizers
    #   transformers
idna==3.10
    # via
    #   -r requirements.txt
    #   anyio
    #   httpx
    #   requests
ipykernel==6.29.5
    # via -r requirements.txt
ipython==9.3.0
    # via
    #   -r requirements.txt
    #   ipykernel
ipython-pygments-lexers==1.1.1
    # via
    #   -r requirements.txt
    #   ipython
itsdangerous==2.2.0
    # via
    #   -r requirements.txt
    #   flask
jedi==0.19.2
    # via
    #   -r requirements.txt
    #   ipython
jinja2==3.1.6
    # via
    #   -r requirements.txt
    #   flask
    #   torch
jiter==0.10.0
    # via
    #   -r requirements.txt
    #   anthropic
    #   openai
jsonpatch==1.33
    # via
    #   -r requirements.txt
    #   langchain-core
jsonpointer==3.0.0
    # via
    #   -r requirements.txt
    #   jsonpatch
jupyter-client==8.6.3
    # via
    #   -r requirements.txt
    #   ipykernel
jupyter-core==5.8.1
    # via
    #   -r requirements.txt
    #   ipykernel
    #   jupyter-client
langchain-anthropic==0.3.15
    # via -r requirements.txt
langchain-core==0.3.65
    # via
    #   -r requirements.txt
    #   langchain-anthropic
    #   langchain-ollama
    #   langchain-openai
    #   langgraph
    #   langgraph-checkpoint
    #   langgraph-prebuilt
langchain-ollama==0.3.3
    # via -r requirements.txt
langchain-openai==0.3.23
    # via -r requirements.txt
langgraph==0.4.8
    # via -r requirements.txt
langgraph-checkpoint==2.1.0
    # via
    #   -r requirements.txt
    #   langgraph
    #   langgraph-prebuilt
langgraph-prebuilt==0.2.2
    # via
    #   -r requirements.txt
    #   langgraph
langgraph-sdk==0.1.70
    # via
    #   -r requirements.txt
    #   langgraph
langsmith==0.3.45
    # via
    #   -r requirements.txt
    #   langchain-core
markupsafe==3.0.2
    # via
    #   -r requirements.txt
    #   flask
    #   jinja2
    #   werkzeug
matplotlib-inline==0.1.7
    # via
    #   -r requirements.txt
    #   ipykernel
    #   ipython
mpmath==1.3.0
    # via
    #   -r requirements.txt
    #   sympy
nest-asyncio==1.6.0
    # via
    #   -r requirements.txt
    #   ipykernel
networkx==3.5
    # via
    #   -r requirements.txt
    #   torch
numpy==2.3.0
    # via
    #   -r requirements.txt
    #   accelerate
    #   transformers
ollama==0.5.1
    # via
    #   -r requirements.txt
    #   langchain-ollama
openai==1.88.0
    # via
    #   -r requirements.txt
    #   langchain-openai
orjson==3.10.18
    # via
    #   -r requirements.txt
    #   langgraph-sdk
    #   langsmith
ormsgpack==1.10.0
    # via
    #   -r requirements.txt
    #   langgraph-checkpoint
packaging==24.2
    # via
    #   -r requirements.txt
    #   accelerate
    #   huggingface-hub
    #   ipykernel
    #   langchain-core
    #   langsmith
    #   transformers
parso==0.8.4
    # via
    #   -r requirements.txt
    #   jedi
pexpect==4.9.0
    # via
    #   -r requirements.txt
    #   ipython
pip==25.1.1
    # via -r requirements.txt
platformdirs==4.3.8
    # via
    #   -r requirements.txt
    #   jupyter-core
prompt-toolkit==3.0.51
    # via
    #   -r requirements.txt
    #   ipython
proto-plus==1.26.1
    # via
    #   -r requirements.txt
    #   google-ai-generativelanguage
    #   google-api-core
protobuf==5.29.5
    # via
    #   -r requirements.txt
    #   google-ai-generativelanguage
    #   google-api-core
    #   google-generativeai
    #   googleapis-common-protos
    #   grpcio-status
    #   proto-plus
psutil==7.0.0
    # via
    #   -r requirements.txt
    #   accelerate
    #   ipykernel
ptyprocess==0.7.0
    # via
    #   -r requirements.txt
    #   pexpect
pure-eval==0.2.3
    # via
    #   -r requirements.txt
    #   stack-data
pyasn1==0.6.1
    # via
    #   -r requirements.txt
    #   pyasn1-modules
    #   rsa
pyasn1-modules==0.4.2
    # via
    #   -r requirements.txt
    #   google-auth
pydantic==2.11.7
    # via
    #   -r requirements.txt
    #   anthropic
    #   google-generativeai
    #   langchain-anthropic
    #   langchain-core
    #   langgraph
    #   langsmith
    #   ollama
    #   openai
pydantic-core==2.33.2
    # via
    #   -r requirements.txt
    #   pydantic
pydeps==3.0.1
    # via -r requirements.txt
pygments==2.19.1
    # via
    #   -r requirements.txt
    #   ipython
    #   ipython-pygments-lexers
pyparsing==3.2.3
    # via
    #   -r requirements.txt
    #   httplib2
python-dateutil==2.9.0.post0
    # via
    #   -r requirements.txt
    #   jupyter-client
python-engineio==4.12.2
    # via
    #   -r requirements.txt
    #   python-socketio
python-socketio==5.13.0
    # via
    #   -r requirements.txt
    #   flask-socketio
pyyaml==6.0.2
    # via
    #   -r requirements.txt
    #   accelerate
    #   huggingface-hub
    #   langchain-core
    #   transformers
pyzmq==27.0.0
    # via
    #   -r requirements.txt
    #   ipykernel
    #   jupyter-client
regex==2024.11.6
    # via
    #   -r requirements.txt
    #   tiktoken
    #   transformers
requests==2.32.4
    # via
    #   -r requirements.txt
    #   google-api-core
    #   huggingface-hub
    #   langsmith
    #   requests-toolbelt
    #   tiktoken
    #   transformers
requests-toolbelt==1.0.0
    # via
    #   -r requirements.txt
    #   langsmith
rsa==4.9.1
    # via
    #   -r requirements.txt
    #   google-auth
safetensors==0.5.3
    # via
    #   -r requirements.txt
    #   accelerate
    #   transformers
setuptools==80.9.0
    # via
    #   -r requirements.txt
    #   torch
simple-websocket==1.1.0
    # via
    #   -r requirements.txt
    #   python-engineio
six==1.17.0
    # via
    #   -r requirements.txt
    #   python-dateutil
sniffio==1.3.1
    # via
    #   -r requirements.txt
    #   anthropic
    #   anyio
    #   openai
stack-data==0.6.3
    # via
    #   -r requirements.txt
    #   ipython
stdlib-list==0.11.1
    # via
    #   -r requirements.txt
    #   pydeps
sympy==1.14.0
    # via
    #   -r requirements.txt
    #   torch
tabulate==0.9.0
    # via -r requirements.txt
tenacity==9.1.2
    # via
    #   -r requirements.txt
    #   langchain-core
termcolor==3.1.0
    # via -r requirements.txt
tiktoken==0.9.0
    # via
    #   -r requirements.txt
    #   langchain-openai
tokenizers==0.21.1
    # via
    #   -r requirements.txt
    #   transformers
torch==2.7.1
    # via
    #   -r requirements.txt
    #   accelerate
tornado==6.5.1
    # via
    #   -r requirements.txt
    #   ipykernel
    #   jupyter-client
tqdm==4.67.1
    # via
    #   -r requirements.txt
    #   google-generativeai
    #   huggingface-hub
    #   openai
    #   transformers
traitlets==5.14.3
    # via
    #   -r requirements.txt
    #   comm
    #   ipykernel
    #   ipython
    #   jupyter-client
    #   jupyter-core
    #   matplotlib-inline
transformers==4.52.4
    # via -r requirements.txt
typing-extensions==4.14.0
    # via
    #   -r requirements.txt
    #   anthropic
    #   anyio
    #   google-generativeai
    #   huggingface-hub
    #   langchain-core
    #   openai
    #   pydantic
    #   pydantic-core
    #   torch
    #   typing-inspection
typing-inspection==0.4.1
    # via
    #   -r requirements.txt
    #   pydantic
uritemplate==4.2.0
    # via
    #   -r requirements.txt
    #   google-api-python-client
urllib3==2.4.0
    # via
    #   -r requirements.txt
    #   requests
wcwidth==0.2.13
    # via
    #   -r requirements.txt
    #   prompt-toolkit
werkzeug==3.1.3
    # via
    #   -r requirements.txt
    #   flask
wsproto==1.2.0
    # via
    #   -r requirements.txt
    #   simple-websocket
xxhash==3.5.0
    # via
    #   -r requirements.txt
    #   langgraph
zstandard==0.23.0
    # via
    #   -r requirements.txt
    #   langsmith
