{"cells": [{"cell_type": "code", "execution_count": 2, "id": "9c142a6c", "metadata": {}, "outputs": [{"data": {"text/plain": ["'/Users/<USER>/vscode/DocAgent/.venv/bin/python'"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["import sys\n", "sys.executable"]}, {"cell_type": "code", "execution_count": 3, "id": "6f714c52", "metadata": {}, "outputs": [{"data": {"text/plain": ["'0.3.45'"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["import langsmith\n", "langsmith.__version__"]}, {"cell_type": "code", "execution_count": null, "id": "d326e62f", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.10"}}, "nbformat": 4, "nbformat_minor": 5}